#!/usr/bin/env node
/*
  Swoop — Headless Simulation
  - Two non-reactive bots play to a target score
  - Configurable rounds and target score
  - Records ~10 key metrics per run and prints a summary

  Usage:
    node src/sim/simulate.js --rounds=100 --target=5 --seed=42
*/

// --- Core board constants & helpers (copied from UI logic) ---
const LANES = [
  { sum: 2, L: 3, basket: true },
  { sum: 3, L: 4, basket: false },
  { sum: 4, L: 5, basket: true },
  { sum: 5, L: 6, basket: false },
  { sum: 6, L: 7, basket: true },
  { sum: 7, L: 8, basket: false },
  { sum: 8, L: 7, basket: true },
  { sum: 9, L: 6, basket: false },
  { sum: 10, L: 5, basket: true },
  { sum: 11, L: 4, basket: false },
  { sum: 12, L: 3, basket: true },
];

function checkpoints(L) {
  const out = [2];
  if (L >= 6) out.push(4);
  out.push(L - 1);
  return [...new Set(out)].filter((x) => x >= 1 && x <= L);
}

function deterrents(L, sum) {
  if (L <= 3) return [];
  const det = [3, L - 2];
  if ((sum === 6 || sum === 8) && L >= 5) det.push(5);
  const cps = checkpoints(L);
  return [...new Set(det)].filter((x) => x >= 1 && x <= L && !cps.includes(x));
}

const oddSlope = { 3: +1, 5: -1, 7: -1, 9: -1, 11: +1 };

// --- RNG with optional seed for reproducibility ---
function mulberry32(a) {
  return function () {
    let t = (a += 0x6d2b79f5);
    t = Math.imul(t ^ (t >>> 15), t | 1);
    t ^= t + Math.imul(t ^ (t >>> 7), t | 61);
    return ((t ^ (t >>> 14)) >>> 0) / 4294967296;
  };
}

function makeRng(seed) {
  if (seed === undefined || seed === null) return Math.random;
  const s = typeof seed === 'number' ? seed >>> 0 : hashStr(seed);
  return mulberry32(s);
}

function hashStr(str) {
  let h = 2166136261 >>> 0;
  for (let i = 0; i < str.length; i++) {
    h ^= str.charCodeAt(i);
    h = Math.imul(h, 16777619);
  }
  return h >>> 0;
}

// --- Game state helpers ---
function initialGame() {
  return {
    players: [
      { name: 'Bot1', side: 'L', score: 0, pieces: [] },
      { name: 'Bot2', side: 'R', score: 0, pieces: [] },
    ],
    current: 0,
    baskets: LANES.map((l) => l.basket),
  };
}

function occupied(game, r, side, step) {
  for (const pl of game.players) {
    if (pl.pieces.some((pc) => pc.r === r && pc.side === side && pc.step === step)) return true;
  }
  return false;
}

function pieceOnLane(pl, r) {
  return pl.pieces.find((p) => p.r === r) || null;
}

function activeCount(pl) {
  return pl.pieces.filter((p) => p.active).length;
}

function ensurePieceForSum(game, pl, sum) {
  const r = LANES.findIndex((x) => x.sum === sum);
  if (r < 0) return null;
  let pc = pieceOnLane(pl, r);
  const side = pl.side;
  if (pc) {
    if (!pc.active && activeCount(pl) < 2) pc.active = true;
    return pc;
  }
  if (pl.pieces.length >= 5 || activeCount(pl) >= 2) return null;
  if (occupied(game, r, side, 1)) return null;
  pc = { r, side, step: 1, carrying: false, active: true };
  pl.pieces.push(pc);
  return pc;
}

function canMoveOnSum(game, pl, sum) {
  const r = LANES.findIndex((x) => x.sum === sum);
  if (r < 0) return false;
  const pc = pieceOnLane(pl, r);
  if (pc) {
    const L = LANES[pc.r].L;
    const dir = pc.carrying ? -1 : +1;
    const ns = pc.step + dir;
    return ns >= 1 && ns <= L && !occupied(game, pc.r, pc.side, ns);
  } else {
    return pl.pieces.length < 5 && !occupied(game, r, pl.side, 1) && activeCount(pl) < 2;
  }
}

function afterMovePickup(game, pc) {
  const lane = LANES[pc.r];
  const L = lane.L;
  if (lane.basket && game.baskets[pc.r] && pc.step === L && !pc.carrying) {
    pc.carrying = true;
    game.baskets[pc.r] = false;
    return true;
  }
  return false;
}

function advanceOne(game, pc) {
  const L = LANES[pc.r].L;
  const dir = pc.carrying ? -1 : +1;
  const ns = pc.step + dir;
  if (ns >= 1 && ns <= L && !occupied(game, pc.r, pc.side, ns)) {
    pc.step = ns;
    afterMovePickup(game, pc);
    return true;
  }
  return false;
}

function potentialSwoops(game, pc) {
  const targets = [];
  const r = pc.r;
  const L = LANES[r].L;
  const sum = LANES[r].sum;
  const atOddTop = sum % 2 === 1 && pc.step === L - 1;
  for (const dr of [-1, +1]) {
    const r2 = r + dr;
    if (r2 < 0 || r2 >= LANES.length) continue;
    let step2 = pc.step;
    if (atOddTop) {
      step2 = Math.min(LANES[r2].L, Math.max(1, pc.step + oddSlope[sum]));
    }
    step2 = Math.min(LANES[r2].L, step2);
    if (!occupied(game, r2, pc.side, step2)) targets.push({ r: r2, step: step2 });
  }
  return targets;
}

function eligibleSwoopPiecesForSum(game, pl, sum) {
  const selectedLaneIndex = LANES.findIndex((lane) => lane.sum === sum);
  if (selectedLaneIndex < 0) return [];
  const adj = [selectedLaneIndex - 1, selectedLaneIndex + 1].filter((i) => i >= 0 && i < LANES.length);
  const adjSums = adj.map((i) => LANES[i].sum);
  return pl.pieces.filter((p) => p.active && adjSums.includes(LANES[p.r].sum));
}

function canSwoopWithSum(game, pl, sum) {
  for (const pc of eligibleSwoopPiecesForSum(game, pl, sum)) {
    if (potentialSwoops(game, pc).length > 0) return true;
  }
  return false;
}

function anyActionForSum(game, pl, sum) {
  return canMoveOnSum(game, pl, sum) || canSwoopWithSum(game, pl, sum);
}

function resolveDeterrents(game, pl) {
  const kept = [];
  for (const pc of pl.pieces) {
    const L = LANES[pc.r].L;
    const sum = LANES[pc.r].sum;
    const dets = deterrents(L, sum);
    const onDet = dets.includes(pc.step);
    if (onDet) {
      if (pc.carrying && LANES[pc.r].basket) game.baskets[pc.r] = true; // return basket
      continue; // removed
    }
    kept.push(pc);
  }
  pl.pieces = kept;
}

function bank(game) {
  const pl = game.players[game.current];
  let delivered = 0;
  const kept = [];

  for (const pc of pl.pieces) {
    const L = LANES[pc.r].L;
    const cps = checkpoints(L);

    // Pick up at top if possible before sliding
    if (pc.step === L && LANES[pc.r].basket && game.baskets[pc.r] && !pc.carrying) {
      pc.carrying = true;
      game.baskets[pc.r] = false;
    }

    if (pc.carrying) {
      if (pc.step === 1) {
        delivered++;
      } else {
        kept.push(pc);
      }
    } else {
      let dest = null;
      for (const c of cps) if (c <= pc.step) dest = c;
      if (dest !== null) {
        pc.step = dest;
        kept.push(pc);
      }
      // if dest is null, the piece falls off (removed)
    }
  }

  pl.pieces = kept;
  pl.score += delivered;
  resolveDeterrents(game, pl);
  for (const p of pl.pieces) p.active = false;
  game.current = 1 - game.current;
  return delivered;
}

// --- Tailwind: simple policy for the non-active player ---
function tailwind(game, metrics) {
  const opp = game.players[1 - game.current];
  const side = opp.side;

  // Try to advance a carrying piece toward home first
  const candidates = [...opp.pieces];
  for (const pc of candidates) {
    const L = LANES[pc.r].L;
    const dir = pc.carrying ? -1 : +1;
    const ns = pc.step + dir;
    if (ns >= 1 && ns <= L && !occupied(game, pc.r, pc.side, ns)) {
      pc.step = ns;
      afterMovePickup(game, pc);
      metrics.tailwindAdvances++;
      return true;
    }
  }

  // Otherwise, spawn if possible
  if (opp.pieces.length < 5) {
    for (let r = 0; r < LANES.length; r++) {
      if (!occupied(game, r, side, 1)) {
        opp.pieces.push({ r, side, step: 1, carrying: false, active: false });
        metrics.tailwindSpawns++;
        return true;
      }
    }
  }

  return false; // no-op
}

// --- Dice ---
function r6(rng) {
  return 1 + Math.floor(rng() * 6);
}

function roll3(rng) {
  const d = [r6(rng), r6(rng), r6(rng)];
  const pairs = [
    { i: 0, j: 1, sum: d[0] + d[1] },
    { i: 0, j: 2, sum: d[0] + d[2] },
    { i: 1, j: 2, sum: d[1] + d[2] },
  ];
  return { d, pairs };
}

// --- Bot policies ---
// Bot 1: prefers highest odd sums (11 > 9 > 7 > 5 > 3), else highest sum; banks when it delivered this turn or after 3 actions
function chooseActionBot1(ctx) {
  const { game, rng } = ctx;
  const pl = game.players[game.current];
  const { pairs } = ctx.roll;

  // Filter pairs with any available action
  const usable = pairs.filter((p) => anyActionForSum(game, pl, p.sum));
  if (usable.length === 0) return { type: 'bust' };

  // Sort by: odd desc weight then sum desc
  usable.sort((a, b) => {
    const ao = a.sum % 2 === 1 ? 1 : 0;
    const bo = b.sum % 2 === 1 ? 1 : 0;
    if (ao !== bo) return bo - ao;
    return b.sum - a.sum;
  });

  const chosen = usable[0];

  // Prefer Move if possible on chosen odd, else Swoop
  if (canMoveOnSum(game, pl, chosen.sum)) return { type: 'move', sum: chosen.sum };
  // Choose a swoop target that ends on a lane with higher odd sum if possible
  const pcs = eligibleSwoopPiecesForSum(game, pl, chosen.sum);
  for (const pc of pcs) {
    const targs = potentialSwoops(game, pc);
    if (targs.length) {
      // pick target by preferring odd lane with higher sum
      targs.sort((a, b) => {
        const la = LANES[a.r].sum;
        const lb = LANES[b.r].sum;
        const ao = la % 2 === 1 ? 1 : 0;
        const bo = lb % 2 === 1 ? 1 : 0;
        if (ao !== bo) return bo - ao;
        return lb - la;
      });
      return { type: 'swoop', sum: chosen.sum, pc, target: targs[0] };
    }
  }
  return { type: 'bust' };
}

// Bot 2: mixed — 50% prefer even-high (baskets), 50% prefer highest sum; randomize move/swoop if both
function chooseActionBot2(ctx) {
  const { game, rng } = ctx;
  const pl = game.players[game.current];
  const { pairs } = ctx.roll;

  const usable = pairs.filter((p) => anyActionForSum(game, pl, p.sum));
  if (usable.length === 0) return { type: 'bust' };

  const preferEven = rng() < 0.5;
  usable.sort((a, b) => {
    if (preferEven) {
      const ae = a.sum % 2 === 0 ? 1 : 0;
      const be = b.sum % 2 === 0 ? 1 : 0;
      if (ae !== be) return be - ae;
    }
    return b.sum - a.sum;
  });

  const chosen = usable[0];
  const canMove = canMoveOnSum(game, pl, chosen.sum);
  const canSwoop = canSwoopWithSum(game, pl, chosen.sum);
  if (canMove && canSwoop) {
    if (rng() < 0.6) return { type: 'move', sum: chosen.sum }; // slight tilt to move
    // pick random eligible swoop
    const pcs = eligibleSwoopPiecesForSum(game, pl, chosen.sum).filter((pc) => potentialSwoops(game, pc).length > 0);
    const pc = pcs[Math.floor(rng() * pcs.length)];
    const targs = potentialSwoops(game, pc);
    const target = targs[Math.floor(rng() * targs.length)];
    return { type: 'swoop', sum: chosen.sum, pc, target };
  }
  if (canMove) return { type: 'move', sum: chosen.sum };
  if (canSwoop) {
    const pcs = eligibleSwoopPiecesForSum(game, pl, chosen.sum).filter((pc) => potentialSwoops(game, pc).length > 0);
    const pc = pcs[Math.floor(rng() * pcs.length)];
    const targs = potentialSwoops(game, pc);
    const target = targs[Math.floor(rng() * targs.length)];
    return { type: 'swoop', sum: chosen.sum, pc, target };
  }
  return { type: 'bust' };
}

// Banking heuristics (non-reactive)
function shouldBankBot1(turnStats) {
  // Bank if delivered this turn or after 3 actions
  return turnStats.deliveredThisTurn > 0 || turnStats.actionsThisTurn >= 3;
}

function shouldBankBot2(turnStats, rng) {
  // Mixed: 20% random bank pressure, 60% bank if delivered, else after 4 actions
  if (turnStats.deliveredThisTurn > 0) return true;
  if (turnStats.actionsThisTurn >= 4) return true;
  return rng() < 0.2;
}

// --- Single turn executor ---
function playTurn(game, bots, rng, metrics, targetScore) {
  const bot = bots[game.current];
  const pl = game.players[game.current];
  const turnStats = { actionsThisTurn: 0, moves: 0, swoops: 0, busts: 0, deliveredThisTurn: 0 };

  // Pre-roll decision: may bank before rolling
  if (bot.shouldBank(turnStats, rng)) {
    const delivered = bank(game);
    turnStats.deliveredThisTurn += delivered;
    metrics.banks++;
    metrics.deliveries += delivered;
    metrics.turns++;
    metrics.turnsByPlayer[game.current]++;
    return;
  }

  // Loop rolling until bank or bust
  while (true) {
    const roll = roll3(rng);
    metrics.rolls++;

    // Check if any pair can do anything at all
    const usable = roll.pairs.filter((p) => anyActionForSum(game, pl, p.sum));
    if (usable.length === 0) {
      // Bust
      // Apply bust consequences similar to bank minus sliding outward (we follow app behavior: skip slide, apply dets, deliveries, deactivate)
      const delivered = applyBust(game);
      turnStats.deliveredThisTurn += delivered;
      metrics.busts++;
      metrics.deliveries += delivered;
      metrics.turns++;
      metrics.turnsByPlayer[game.current]++;
      return;
    }

    const ctx = { game, rng, roll };
    const decision = bot.chooseAction(ctx);
    if (decision.type === 'move') {
      // Ensure or move piece
      const pc = ensurePieceForSum(game, pl, decision.sum);
      if (!pc) {
        // If ensure failed (capacity), try next best: treat as no-op and proceed to next roll
        // In practice, this is rare given conditions
      } else {
        const had = pc.step;
        if (pieceOnLane(pl, pc.r) && pc.step === had) {
          // existing piece — advance once
          if (advanceOne(game, pc)) {
            turnStats.moves++;
            turnStats.actionsThisTurn++;
            if (pc.carrying && pc.step === 1) {
              // delivery realizes only on bank/bust; we count on event
            }
          }
        } else {
          // freshly ensured; counts as action in our accounting
          turnStats.moves++;
          turnStats.actionsThisTurn++;
        }
      }
    } else if (decision.type === 'swoop') {
      // Apply swoop
      const pc = decision.pc;
      if (pc) {
        pc.r = decision.target.r;
        pc.step = decision.target.step;
        turnStats.swoops++;
        turnStats.actionsThisTurn++;
        metrics.tailwindEvents++;
        // Tailwind immediate reaction by opponent
        tailwind(game, metrics);
      }
    } else if (decision.type === 'bust') {
      const delivered = applyBust(game);
      turnStats.deliveredThisTurn += delivered;
      metrics.busts++;
      metrics.deliveries += delivered;
      metrics.turns++;
      metrics.turnsByPlayer[game.current]++;
      return;
    }

    // Optional bank after action (pre-roll in rules, but for simulation we emulate pressure to stop now)
    if (bot.shouldBank(turnStats, rng)) {
      const delivered = bank(game);
      turnStats.deliveredThisTurn += delivered;
      metrics.banks++;
      metrics.deliveries += delivered;
      metrics.turns++;
      metrics.turnsByPlayer[game.current]++;
      return;
    }
  }
}

function applyBust(game) {
  const pl = game.players[game.current];
  // On bust, skip sliding; apply deterrents, then deliveries at step 1; deactivate; pass turn
  // Deliveries
  let delivered = 0;
  const kept = [];
  for (const pc of pl.pieces) {
    if (pc.carrying && pc.step === 1) {
      delivered++;
    } else {
      kept.push(pc);
    }
  }
  pl.pieces = kept;
  pl.score += delivered;
  resolveDeterrents(game, pl);
  for (const p of pl.pieces) p.active = false;
  game.current = 1 - game.current;
  return delivered;
}

// --- One full game to target score ---
function playGame(targetScore, rng, bots) {
  const game = initialGame();

  const metrics = {
    turns: 0,
    turnsByPlayer: [0, 0],
    rolls: 0,
    busts: 0,
    banks: 0,
    tailwindEvents: 0,
    tailwindAdvances: 0,
    tailwindSpawns: 0,
    deliveries: 0,
  };

  while (game.players[0].score < targetScore && game.players[1].score < targetScore) {
    playTurn(game, bots, rng, metrics, targetScore);
  }

  const winner = game.players[0].score >= targetScore ? 0 : 1;
  return { winner, metrics, game };
}

// --- Run many rounds ---
function runSimulation({ rounds, target, seed }) {
  const rng = makeRng(seed);

  const bots = [
    { chooseAction: chooseActionBot1, shouldBank: (t) => shouldBankBot1(t) },
    { chooseAction: chooseActionBot2, shouldBank: (t, r) => shouldBankBot2(t, rng) },
  ];

  const agg = {
    games: 0,
    wins: [0, 0],
    turns: 0,
    turnsByPlayer: [0, 0],
    rolls: 0,
    busts: 0,
    banks: 0,
    tailwindEvents: 0,
    tailwindAdvances: 0,
    tailwindSpawns: 0,
    deliveries: 0,
  };

  for (let i = 0; i < rounds; i++) {
    const { winner, metrics } = playGame(target, rng, bots);
    agg.games++;
    agg.wins[winner]++;
    agg.turns += metrics.turns;
    agg.turnsByPlayer[0] += metrics.turnsByPlayer[0];
    agg.turnsByPlayer[1] += metrics.turnsByPlayer[1];
    agg.rolls += metrics.rolls;
    agg.busts += metrics.busts;
    agg.banks += metrics.banks;
    agg.tailwindEvents += metrics.tailwindEvents;
    agg.tailwindAdvances += metrics.tailwindAdvances;
    agg.tailwindSpawns += metrics.tailwindSpawns;
    agg.deliveries += metrics.deliveries;
  }

  // Derived metrics
  const summary = {
    games_played: agg.games,
    wins_bot1: agg.wins[0],
    wins_bot2: agg.wins[1],
    win_rate_bot1: agg.wins[0] / agg.games,
    avg_turns_per_game: agg.turns / agg.games,
    avg_turns_bot1: agg.turnsByPlayer[0] / agg.games,
    avg_turns_bot2: agg.turnsByPlayer[1] / agg.games,
    avg_rolls_per_game: agg.rolls / agg.games,
    avg_busts_per_game: agg.busts / agg.games,
    avg_banks_per_game: agg.banks / agg.games,
    avg_tailwind_events_per_game: agg.tailwindEvents / agg.games,
    avg_tailwind_advances_per_game: agg.tailwindAdvances / agg.games,
    avg_tailwind_spawns_per_game: agg.tailwindSpawns / agg.games,
    avg_deliveries_per_game: agg.deliveries / agg.games,
    notes: [
      'Bots are non-reactive. Future: adaptive banking based on risk, lane congestion awareness, opponent threat modeling, basket scarcity awareness, swoop value estimation, deterrent avoidance, piece activation management, tailwind optimization, roll-pair selection under blockers, endgame heuristics.'
    ],
  };

  return summary;
}

// --- CLI ---
function parseArgs(argv) {
  const out = { rounds: 100, target: 5, seed: undefined };
  for (const a of argv.slice(2)) {
    const [k, v] = a.split('=');
    if (k === '--rounds') out.rounds = Number(v);
    else if (k === '--target') out.target = Number(v);
    else if (k === '--seed') out.seed = isNaN(Number(v)) ? v : Number(v);
  }
  return out;
}

if (require.main === module) {
  const opts = parseArgs(process.argv);
  const summary = runSimulation(opts);
  // Pretty print key metrics
  console.log(JSON.stringify(summary, null, 2));
}

module.exports = { runSimulation };

